// React
import React from 'react';

// Core
import { TestDifficulty, TestSeniorityLevel, EnumText, CustomIcon, Card } from '/src';

// Flowbite
import { Checkbox as FlowbiteCheckbox } from 'flowbite-react';

export const AssessmentCard = ({
  assessment,
  isSelected,
  onSelectionChange,
  onNavigate,
  showNavigation = true,
  type = 'interview' // 'screening', 'test', 'interview'
}) => {
  const handleCardClick = (e) => {
    // Prevent navigation when clicking on checkbox
    if (e.target.type === 'checkbox') {
      return;
    }
    if (showNavigation && onNavigate) {
      onNavigate(assessment);
    }
  };

  const handleCheckboxChange = (e) => {
    e.stopPropagation();
    onSelectionChange(assessment._id, e.target.checked);
  };

  return (
    <Card
      className={`relative p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${
        isSelected ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20' : 'border-gray-200 dark:border-gray-600'
      }`}
      onClick={handleCardClick}
    >
      {/* Selection Checkbox */}
      <div className="absolute top-4 right-4">
        <FlowbiteCheckbox
          checked={isSelected}
          onChange={handleCheckboxChange}
          className="cursor-pointer text-purple-500"
        />
      </div>

      {/* Content */}
      <div className="pr-8 space-y-3">
        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-1">
          {assessment.title}
        </h3>

        {/* Description */}
        {assessment.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
            {assessment.description}
          </p>
        )}

        {/* Badges Row */}
        <div className="flex items-center gap-2 flex-wrap">
          {/* Type Badge */}
          <span className={`px-2 py-1 text-xs font-medium rounded-md ${
            type === 'interview' ? 'bg-blue-100 text-blue-800' :
            type === 'test' ? 'bg-green-100 text-green-800' :
            'bg-purple-100 text-purple-800'
          }`}>
            {type === 'interview' ? 'Interview' : type === 'test' ? 'Test' : 'Screening'}
          </span>

          {/* Difficulty Badge */}
          {assessment.difficulty && <TestDifficulty difficulty={assessment.difficulty} />}

          {/* Seniority Level Badge */}
          {assessment.seniorityLevel && <TestSeniorityLevel seniorityLevel={assessment.seniorityLevel} />}
        </div>

        {/* Stats Row */}
        <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
          {/* Questions */}
          <div className="flex items-center gap-1">
            <CustomIcon definedIcon="questionInBorder" width="16" height="16" />
            <span className="font-medium">{assessment.numOfQuestions}</span>
            <span>{assessment.numOfQuestions > 1 ? 'questions' : 'question'}</span>
          </div>

          {/* Duration */}
          <div className="flex items-center gap-1">
            <CustomIcon definedIcon="clockThree" width="16" height="16" />
            <span className="font-medium">{assessment.duration}</span>
            <span>mins</span>
          </div>
        </div>

        {/* Technology Tags */}
        {assessment.categoryName && assessment.categoryName.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap">
            {assessment.categoryName.slice(0, 3).map((category, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs font-medium rounded-md"
              >
                {category}
              </span>
            ))}
            {assessment.categoryName.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs font-medium rounded-md">
                +{assessment.categoryName.length - 3} more
              </span>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};
